services:
  nginx:
    build:
      context: ..
      dockerfile: docker/nginx/Dockerfile
    image: nginx-${COMPOSE_PROJECT_NAME}
    environment:
      - PUBLIC_DOMAIN=${PUBLIC_DOMAIN}
      - EXTERNAL_PORT=${EXTERNAL_PORT}
    ports:
      - "${NGINX_PORT:-80}:80"
    profiles:
      - docker
    networks:
      - ii

  frontend:
    build:
      context: ..
      dockerfile: docker/frontend/Dockerfile
    image: frontend-${COMPOSE_PROJECT_NAME}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - ${WORKSPACE_PATH:-../workspace}:/app/workspace
    networks:
      - ii

  sandbox:
    build:
      context: ..
      dockerfile: docker/sandbox/Dockerfile
    image: sandbox-${COMPOSE_PROJECT_NAME}
    environment:
      - WDS_SOCKET_PORT=0
    ports:
      - "${SANDBOX_PORT:-17300}:17300" # Sandbox server port
      - "${CODE_SERVER_PORT:-9000}:9000" # Code-server port
    volumes:
      - ${WORKSPACE_PATH:-../workspace}:/workspace
    profiles:
      - docker
    deploy:
      replicas: 0
    networks:
      - ii

  backend:
    build:
      context: ..
      dockerfile: docker/backend/Dockerfile
    image: backend-${COMPOSE_PROJECT_NAME}
    init: true
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    env_file:
      - ../.env
    environment:
      - COMPOSE_PROJECT_NAME=${COMPOSE_PROJECT_NAME}
      - BASE_URL=${BASE_URL}
      #Path of mounted file
      - GOOGLE_APPLICATION_CREDENTIALS=/app/google-application-credentials.json
      - PROJECT_ID=${PROJECT_ID}
      - REGION=${REGION}
      - WORKSPACE_PATH=${WORKSPACE_PATH}
      - USE_DOCKER_SANDBOX=${USE_DOCKER_SANDBOX}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock #ONLY WORK FOR UNIX FOR NOW
      - ${WORKSPACE_PATH:-../workspace}:/app/workspace
      #If file doesn't exist, use a dummy file
      - ${GOOGLE_APPLICATION_CREDENTIALS:-.dummy-credentials.json}:/app/google-application-credentials.json
      - ../db:/app/db
      - ~/.ii_agent:/.ii_agent
    networks:
      - ii

networks:
  ii:
    name: ii-${COMPOSE_PROJECT_NAME}
