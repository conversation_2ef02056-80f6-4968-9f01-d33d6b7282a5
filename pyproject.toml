[project]
name = "ii-agent"
version = "0.1.0"
description = "Intelligent Agent platform providing CLI and WebSocket interfaces"
readme = "README.md"
authors = [
    { name = "Intelligent Internet", email = "<EMAIL>" }
]
requires-python = ">=3.10"
dependencies = [
    "anthropic[vertex]>=0.50.0",
    "dataclasses-json>=0.6.7",
    "duckduckgo-search>=8.0.1",
    "fastapi>=0.115.12",
    "google-cloud-aiplatform>=1.90.0",
    "google-genai>=1.14.0",
    "ii-researcher>=0.1.5",
    "jsonschema>=4.23.0",
    "mammoth>=1.9.0",
    "markdownify>=1.1.0",
    "pandas>=2.2.3",
    "pathvalidate>=3.2.3",
    "pdfminer-six>=20250506",
    "openai>=1.68.2",
    "pexpect>=4.9.0",
    "pillow>=11.2.1",
    "pip>=25.1.1",
    "playwright>=1.52.0",
    "prompt-toolkit>=3.0.51",
    "puremagic>=1.29",
    "pydub>=0.25.1",
    "pymupdf>=1.25.5",
    "pytest>=8.3.5",
    "python-dotenv>=1.1.0",
    "python-pptx>=1.0.2",
    "rich==13.7.1",
    "speechrecognition>=3.14.2",
    "tavily-python>=0.7.2",
    "tenacity>=9.1.2",
    "termcolor>=3.0.1",
    "uvicorn[standard]>=0.29.0,<0.30.0",
    "youtube-transcript-api>=1.0.3",
    "docker>=7.1.0",
    "e2b-code-interpreter>=1.5.1",
    "alembic>=1.16.1",
]

[project.optional-dependencies]
gaia = [
    "datasets>=3.6.0",
    "huggingface-hub>=0.31.1",
    "sqlalchemy>=2.0.0",
    "tavily-python>=0.7.2",
    "tenacity>=9.1.2",
    "termcolor>=3.0.1",
    "uvicorn[standard]>=0.29.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/ii_agent"]
[dependency-groups]
dev = [
    "pytest-asyncio>=1.0.0",
]
